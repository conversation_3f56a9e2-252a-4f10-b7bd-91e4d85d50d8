TestFile: refund.integration.test.js
---
# 全额退款（取消订单）测试组
TestFunction: cancelOrderWithRefund
Cases:
  - CaseID: "R-CANCEL-001"
    Module: "refund"
    Description: "商家原因全额退款 - 缺货"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "smoke"
      - "positive"
      - "full-refund"
    Precondition:
      - "创建已支付的Stripe订单"
      - "设置餐厅管理员认证token"
      - "Mock Stripe退款API返回成功"
      - "启动MongoDB和Redis容器"
    Steps:
      - "调用cancelOrder mutation"
      - "传入orderId和reason: MERCHANT_OUT_OF_STOCK"
      - "验证退款记录创建"
      - "验证Stripe API调用"
      - "模拟Stripe webhook回调"
    ExpectedResult:
      - "订单状态变为CANCELLED"
      - "退款记录状态为SUCCEEDED"
      - "客户收到全额退款"
      - "商家承担交易手续费"
      - "发送退款成功通知"

  - CaseID: "R-CANCEL-002"
    Module: "refund"
    Description: "客户原因全额退款"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "smoke"
      - "positive"
      - "full-refund"
    Precondition:
      - "创建已支付的Stripe订单"
      - "设置餐厅管理员认证token"
      - "Mock Stripe退款API返回成功"
    Steps:
      - "调用cancelOrder mutation"
      - "传入reason: CUSTOMER_CANCELLED"
      - "验证手续费计算逻辑"
    ExpectedResult:
      - "订单状态变为CANCELLED"
      - "客户承担按比例分摊的手续费"
      - "退款金额 = 订单金额 - 手续费"

  - CaseID: "R-CANCEL-003"
    Module: "refund"
    Description: "取消未支付订单"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "edge-case"
    Precondition:
      - "创建未支付订单（paymentStatus: PENDING）"
      - "设置餐厅管理员认证token"
    Steps:
      - "调用cancelOrder mutation"
      - "传入orderId和退款原因"
    ExpectedResult:
      - "订单状态变为CANCELLED"
      - "不创建退款记录"
      - "不调用Stripe API"

  - CaseID: "R-CANCEL-004"
    Module: "refund"
    Description: "权限验证失败"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "security"
      - "negative"
    Precondition:
      - "创建已支付订单"
      - "使用其他餐厅的认证token"
    Steps:
      - "调用cancelOrder mutation"
      - "尝试取消其他餐厅的订单"
    ExpectedResult:
      - "返回403权限错误"
      - "订单状态不变"
      - "不创建退款记录"

---
# 部分退款测试组
TestFunction: partialRefund
Cases:
  - CaseID: "R-PARTIAL-001"
    Module: "refund"
    Description: "商家原因部分退款"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "smoke"
      - "positive"
      - "partial-refund"
    Precondition:
      - "创建已支付订单（金额100.00）"
      - "设置餐厅管理员认证token"
      - "Mock Stripe退款API返回成功"
    Steps:
      - "调用refundOrder mutation"
      - "传入amount: 30.00, reason: MERCHANT_OTHER"
      - "传入reasonText: '部分商品缺货'"
      - "验证退款计算逻辑"
    ExpectedResult:
      - "订单状态变为PARTIALLY_REFUNDED"
      - "退款记录创建成功"
      - "客户收到30.00退款"
      - "商家承担手续费"
      - "订单totalRefunded更新为30.00"

  - CaseID: "R-PARTIAL-002"
    Module: "refund"
    Description: "客户原因部分退款"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "partial-refund"
    Precondition:
      - "创建已支付订单（金额100.00）"
      - "设置餐厅管理员认证token"
    Steps:
      - "调用refundOrder mutation"
      - "传入amount: 25.00, reason: CUSTOMER_CANCELLED"
    ExpectedResult:
      - "客户承担按比例手续费"
      - "实际退款金额 < 25.00"
      - "feeBearer设置为CUSTOMER"

  - CaseID: "R-PARTIAL-003"
    Module: "refund"
    Description: "多次部分退款"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "complex"
    Precondition:
      - "创建已支付订单（金额100.00）"
      - "已有一次部分退款（30.00）"
    Steps:
      - "调用refundOrder mutation"
      - "传入amount: 20.00"
      - "验证可退款余额计算"
    ExpectedResult:
      - "订单totalRefunded更新为50.00"
      - "可退款余额为50.00"
      - "创建第二条退款记录"

  - CaseID: "R-PARTIAL-004"
    Module: "refund"
    Description: "超额退款验证"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "validation"
    Precondition:
      - "创建已支付订单（金额100.00）"
      - "已有部分退款（80.00）"
    Steps:
      - "调用refundOrder mutation"
      - "传入amount: 30.00（超过可退款余额）"
    ExpectedResult:
      - "返回验证错误"
      - "错误信息包含可退款余额"
      - "不创建退款记录"

---
# Stripe集成测试组
TestFunction: stripeIntegration
Cases:
  - CaseID: "R-STRIPE-001"
    Module: "stripe"
    Description: "Stripe退款API调用成功"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "integration"
      - "stripe"
    Precondition:
      - "配置Stripe测试环境"
      - "创建真实的Stripe支付意图"
      - "订单关联Stripe支付ID"
    Steps:
      - "发起部分退款请求"
      - "调用Stripe退款API"
      - "验证Stripe响应"
    ExpectedResult:
      - "Stripe返回退款对象"
      - "退款状态为pending或succeeded"
      - "退款记录保存stripeRefundId"

  - CaseID: "R-STRIPE-002"
    Module: "stripe"
    Description: "Stripe退款API调用失败"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "integration"
      - "negative"
      - "stripe"
    Precondition:
      - "Mock Stripe API返回错误"
      - "创建已支付订单"
    Steps:
      - "发起退款请求"
      - "Stripe API返回错误"
      - "验证错误处理"
    ExpectedResult:
      - "退款记录状态为FAILED"
      - "记录错误信息"
      - "不更新订单状态"

  - CaseID: "R-STRIPE-003"
    Module: "stripe"
    Description: "Webhook事件处理"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "integration"
      - "webhook"
      - "stripe"
    Precondition:
      - "创建处理中的退款记录"
      - "配置Webhook签名验证"
    Steps:
      - "发送refund.updated webhook"
      - "传入status: succeeded"
      - "验证签名"
      - "处理webhook事件"
    ExpectedResult:
      - "退款记录状态更新为SUCCEEDED"
      - "订单状态相应更新"
      - "发送成功通知"

---
# 查询接口测试组
TestFunction: refundQueries
Cases:
  - CaseID: "R-QUERY-001"
    Module: "query"
    Description: "查询退款记录"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
    Precondition:
      - "创建多条退款记录"
      - "设置餐厅管理员认证"
    Steps:
      - "调用getRefund query"
      - "传入refundId"
      - "验证权限检查"
    ExpectedResult:
      - "返回完整退款信息"
      - "包含关联订单信息"
      - "只能查询自己餐厅的退款"

  - CaseID: "R-QUERY-002"
    Module: "query"
    Description: "查询订单退款历史"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
    Precondition:
      - "创建有多次退款的订单"
    Steps:
      - "调用getOrderRefunds query"
      - "传入orderId"
    ExpectedResult:
      - "返回所有退款记录"
      - "按时间倒序排列"
      - "包含退款状态和金额"

---
# 错误处理和边界测试组
TestFunction: errorHandling
Cases:
  - CaseID: "R-ERROR-001"
    Module: "validation"
    Description: "无效退款金额"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "validation"
    Precondition:
      - "创建已支付订单"
    Steps:
      - "调用refundOrder mutation"
      - "传入负数金额或零"
    ExpectedResult:
      - "返回验证错误"
      - "不创建退款记录"

  - CaseID: "R-ERROR-002"
    Module: "concurrency"
    Description: "并发退款请求"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "concurrency"
    Precondition:
      - "创建已支付订单"
    Steps:
      - "同时发起多个退款请求"
      - "验证并发控制"
    ExpectedResult:
      - "只有一个退款请求成功"
      - "其他请求返回冲突错误"

  - CaseID: "R-ERROR-003"
    Module: "network"
    Description: "网络超时处理"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "timeout"
    Precondition:
      - "Mock Stripe API超时"
    Steps:
      - "发起退款请求"
      - "等待超时"
    ExpectedResult:
      - "退款记录状态为FAILED"
      - "记录超时错误信息"

---
# 通知系统测试组
TestFunction: notifications
Cases:
  - CaseID: "R-NOTIFY-001"
    Module: "notification"
    Description: "退款成功通知"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "notification"
    Precondition:
      - "配置通知服务"
      - "创建退款成功记录"
    Steps:
      - "触发退款成功事件"
      - "验证通知发送"
    ExpectedResult:
      - "发送客户通知"
      - "包含实际退款金额"
      - "包含到账时间说明"

  - CaseID: "R-NOTIFY-002"
    Module: "notification"
    Description: "退款失败通知"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "notification"
    Precondition:
      - "创建退款失败记录"
    Steps:
      - "触发退款失败事件"
    ExpectedResult:
      - "发送失败通知"
      - "包含失败原因"
      - "提供后续处理建议"
