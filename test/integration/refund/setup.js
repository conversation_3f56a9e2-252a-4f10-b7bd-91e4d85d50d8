/**
 * Jest测试环境设置
 * 配置测试环境变量和全局设置
 */

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.MONGODB_URI = 'mongodb://localhost:27017/test_refund';
process.env.REDIS_URL = 'redis://localhost:6379';
process.env.STRIPE_SECRET_KEY = 'sk_test_51RC9DgRw6scKj0ZOTdVY2bJAekzcYb3173ewXVnOwfaxceofKBRIK6cpDKNTquKMgM3xEjwg87AgZ6JXS0HEGuSU00BuOEDr1z';

// 禁用日志输出以保持测试输出清洁
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  // 只在非详细模式下禁用日志
  if (!process.env.VERBOSE_TESTS) {
    console.log = jest.fn();
    console.error = jest.fn();
    console.warn = jest.fn();
  }
});

afterAll(() => {
  // 恢复原始console方法
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// 全局测试超时设置
jest.setTimeout(60000);

// 处理未捕获的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Mock外部服务
jest.mock('../../../helpers/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

// Mock通知服务
jest.mock('../../../services/notificationService', () => ({
  sendRefundNotification: jest.fn().mockResolvedValue(true),
  sendSMS: jest.fn().mockResolvedValue(true),
  sendWhatsApp: jest.fn().mockResolvedValue(true)
}));

// 全局测试工具函数
global.sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

global.expectToBeWithinRange = (actual, expected, tolerance = 0.01) => {
  expect(Math.abs(actual - expected)).toBeLessThanOrEqual(tolerance);
};

global.expectDateToBeRecent = (date, withinSeconds = 10) => {
  const now = new Date();
  const diff = Math.abs(now - new Date(date)) / 1000;
  expect(diff).toBeLessThanOrEqual(withinSeconds);
};
