module.exports = {
  displayName: 'Refund Integration Tests',
  testEnvironment: 'node',
  testMatch: [
    '<rootDir>/test/integration/refund/**/*.test.js'
  ],
  setupFilesAfterEnv: [
    '<rootDir>/test/integration/refund/setup.js'
  ],
  testTimeout: 60000, // 60秒超时，用于容器启动
  verbose: true,
  collectCoverage: true,
  coverageDirectory: '<rootDir>/coverage/refund-integration',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    'services/refund*.js',
    'services/stripeRefundService.js',
    'graphql/resolvers/refund.js',
    'models/refund.js',
    'routes/stripe.js'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1'
  },
  globalSetup: '<rootDir>/test/integration/refund/globalSetup.js',
  globalTeardown: '<rootDir>/test/integration/refund/globalTeardown.js'
};
