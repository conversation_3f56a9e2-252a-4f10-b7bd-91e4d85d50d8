const RefundTestHelpers = require('./refund.test.helpers');
const restaurantResolver = require('../../../graphql/resolvers/restaurant');
const refundService = require('../../../services/refundService');
const stripeRefundService = require('../../../services/stripeRefundService');
const { REFUND_REASON, ORDER_STATUS, REFUND_STATUS } = require('../../../helpers/enum');

describe('Cancel Order with Refund Integration Tests', () => {
  let testHelpers;

  beforeAll(async () => {
    testHelpers = new RefundTestHelpers();
    await testHelpers.startContainers();
  });

  afterAll(async () => {
    await testHelpers.stopContainers();
  });

  beforeEach(async () => {
    await testHelpers.cleanupTestData();
    
    // Mock Stripe service
    jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
      id: 're_test_mock_refund',
      amount: 10000, // $100.00 in cents
      status: 'pending'
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('R-CANCEL-001: 商家原因全额退款 - 缺货', () => {
    test('should successfully cancel order with full refund for merchant out of stock', async () => {
      // Precondition: 创建已支付的Stripe订单
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps: 调用cancelOrder mutation
      const args = {
        _id: order._id.toString(),
        reason: REFUND_REASON.MERCHANT_OUT_OF_STOCK
      };

      const result = await restaurantResolver.Mutation.cancelOrder(null, args, context);

      // Expected Results
      expect(result).toBeTruthy();
      expect(result.orderStatus).toBe(ORDER_STATUS.CANCELLED);
      expect(result.reason).toBe(REFUND_REASON.MERCHANT_OUT_OF_STOCK);

      // 验证退款记录创建
      const updatedOrder = await testHelpers.verifyOrderStatus(
        order._id,
        ORDER_STATUS.CANCELLED,
        'FULL'
      );

      expect(updatedOrder.refunds).toHaveLength(1);
      expect(updatedOrder.totalRefunded).toBe(100.00);

      // 验证Stripe API调用
      expect(stripeRefundService.createRefund).toHaveBeenCalledWith(
        order.paymentId,
        100.00,
        'requested_by_customer',
        expect.any(Object)
      );
    });
  });

  describe('R-CANCEL-002: 客户原因全额退款', () => {
    test('should handle customer cancellation with fee deduction', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Mock refund calculation to return customer fee bearing
      const expectedFee = testHelpers.calculateExpectedFee(100.00);
      const expectedRefundAmount = 100.00 - expectedFee;

      jest.spyOn(stripeRefundService, 'createRefund').mockResolvedValue({
        id: 're_test_customer_refund',
        amount: Math.round(expectedRefundAmount * 100), // in cents
        status: 'pending'
      });

      // Steps
      const args = {
        _id: order._id.toString(),
        reason: REFUND_REASON.CUSTOMER_CANCELLED
      };

      const result = await restaurantResolver.Mutation.cancelOrder(null, args, context);

      // Expected Results
      expect(result.orderStatus).toBe(ORDER_STATUS.CANCELLED);
      
      // 验证手续费计算逻辑
      const updatedOrder = await testHelpers.verifyOrderStatus(
        order._id,
        ORDER_STATUS.CANCELLED
      );

      // 客户应该承担手续费
      expect(updatedOrder.totalRefunded).toBeLessThan(100.00);
    });
  });

  describe('R-CANCEL-003: 取消未支付订单', () => {
    test('should cancel unpaid order without creating refund', async () => {
      // Precondition: 创建未支付订单
      const order = await testHelpers.createUnpaidOrder(100.00);
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Steps
      const args = {
        _id: order._id.toString(),
        reason: REFUND_REASON.MERCHANT_OUT_OF_STOCK
      };

      const result = await restaurantResolver.Mutation.cancelOrder(null, args, context);

      // Expected Results
      expect(result.orderStatus).toBe(ORDER_STATUS.CANCELLED);

      // 验证不创建退款记录
      const updatedOrder = await testHelpers.verifyOrderStatus(
        order._id,
        ORDER_STATUS.CANCELLED,
        'NONE'
      );

      expect(updatedOrder.refunds).toHaveLength(0);
      expect(updatedOrder.totalRefunded).toBe(0);

      // 验证不调用Stripe API
      expect(stripeRefundService.createRefund).not.toHaveBeenCalled();
    });
  });

  describe('R-CANCEL-004: 权限验证失败', () => {
    test('should deny access when trying to cancel other restaurant order', async () => {
      // Precondition: 创建订单和不同的餐厅认证
      const order = await testHelpers.createPaidOrder(100.00);
      const otherRestaurant = await testHelpers.createTestRestaurant();
      const context = testHelpers.createGraphQLContext(otherRestaurant._id);

      // Steps
      const args = {
        _id: order._id.toString(),
        reason: REFUND_REASON.MERCHANT_OUT_OF_STOCK
      };

      // Expected Results: 应该抛出权限错误
      await expect(
        restaurantResolver.Mutation.cancelOrder(null, args, context)
      ).rejects.toThrow('Access denied');

      // 验证订单状态不变
      const unchangedOrder = await testHelpers.verifyOrderStatus(
        order._id,
        ORDER_STATUS.PENDING
      );

      expect(unchangedOrder.refunds).toHaveLength(0);
      expect(stripeRefundService.createRefund).not.toHaveBeenCalled();
    });
  });

  describe('Stripe Integration Error Handling', () => {
    test('should handle Stripe API failure gracefully', async () => {
      // Precondition
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      const context = testHelpers.createGraphQLContext(order.restaurantId);

      // Mock Stripe API failure
      jest.spyOn(stripeRefundService, 'createRefund').mockRejectedValue(
        new Error('Stripe API error: insufficient_funds')
      );

      // Steps
      const args = {
        _id: order._id.toString(),
        reason: REFUND_REASON.MERCHANT_OUT_OF_STOCK
      };

      // 取消订单应该成功，但退款失败
      const result = await restaurantResolver.Mutation.cancelOrder(null, args, context);

      // Expected Results
      expect(result.orderStatus).toBe(ORDER_STATUS.CANCELLED);

      // 验证订单被取消但退款可能失败
      const updatedOrder = await testHelpers.verifyOrderStatus(
        order._id,
        ORDER_STATUS.CANCELLED
      );

      // 应该记录退款尝试
      expect(stripeRefundService.createRefund).toHaveBeenCalled();
    });
  });

  describe('Webhook Processing', () => {
    test('should process refund success webhook correctly', async () => {
      // Precondition: 创建处理中的退款
      const order = await testHelpers.createPaidOrder(100.00, 'STRIPE');
      
      // 模拟发起退款
      const refundResult = await refundService.initiateRefund(
        order._id,
        null,
        REFUND_REASON.MERCHANT_OUT_OF_STOCK,
        null,
        'FULL',
        order.restaurantId
      );

      const stripeRefundId = 're_test_webhook_success';

      // Steps: 处理webhook事件
      await refundService.processRefundWebhook(
        stripeRefundId,
        'succeeded',
        { id: stripeRefundId, status: 'succeeded' }
      );

      // Expected Results: 验证退款状态更新
      // Note: 这个测试需要实际的退款记录，可能需要调整
    });
  });
});
